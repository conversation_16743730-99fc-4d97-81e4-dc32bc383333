{"name": "zimbora-new", "main": "expo-router/entry", "version": "1.5.13", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build:android": "npx eas-cli@latest build --platform android --profile production", "submit:android": "npx eas-cli@latest submit --platform android", "deploy:ios": "npx eas-cli@latest build --platform ios --profile production --auto-submit", "expo:update": "npx eas-cli@latest update --channel production --environment production --clear-cache", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@clerk/clerk-expo": "^2.7.8", "@clerk/types": "^4.46.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.66.9", "dayjs": "^1.11.13", "expo": "^53.0.11", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-notifications": "~0.31.3", "expo-random": "^14.0.1", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-updates": "~0.28.14", "expo-web-browser": "~14.1.6", "i18next": "^25.2.1", "native-base": "^3.4.28", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.3", "react-native": "0.79.3", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-otp-entry": "^1.8.4", "react-native-reanimated": "~3.17.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwind-merge": "^3.0.2", "tailwindcss": "3.4.17", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "eas-build-cache-provider": "^16.4.2", "jest": "^29.2.1", "jest-expo": "~53.0.7", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}}