import { useState, useEffect } from "react";
import { SignedIn, SignedOut, useClerk, useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import { createHandleErrorDialog } from "@/lib/errors";
import { z } from "zod";
import { API_URLS, fetchApi } from "@/config/api";
import { TFunction } from "i18next";
import { useTranslation } from "react-i18next";

export const myUserProfileSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    id: z.string({
      invalid_type_error: t("profile.validation.invalid_type", {
        field: t("profile.fields.id"),
      }),
    }),
    name: z.string({
      invalid_type_error: t("profile.validation.invalid_type", {
        field: t("profile.fields.first_name"),
      }),
    }),
    firstName: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.first_name"),
        }),
      })
      .min(
        2,
        t("profile.validation.min", {
          field: t("profile.fields.first_name"),
          min: 2,
        })
      )
      .max(
        50,
        t("profile.validation.max", {
          field: t("profile.fields.first_name"),
          max: 50,
        })
      ),
    lastName: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.last_name"),
        }),
      })
      .min(
        2,
        t("profile.validation.min", {
          field: t("profile.fields.last_name"),
          min: 2,
        })
      )
      .max(
        50,
        t("profile.validation.max", {
          field: t("profile.fields.last_name"),
          max: 50,
        })
      ),
    email: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.email"),
        }),
      })
      .email(
        t("profile.validation.invalid_type", {
          field: t("profile.fields.email"),
        })
      ),
    emailVerified: z.boolean(),
    phoneNumber: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.phone_number"),
        }),
        required_error: t("profile.validation.required", {
          field: t("profile.fields.phone_number"),
        }),
      })
      .length(
        9,
        t("profile.validation.invalid_type", {
          field: t("profile.fields.phone_number"),
        })
      ),
    website: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.website"),
        }),
      })
      .url(
        t("profile.validation.invalid_type", {
          field: t("profile.fields.website"),
        })
      )
      .optional(),
    gender: z
      .enum(["male", "female", "prefer_not_say"], {
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.gender"),
        }),
        required_error: t("profile.validation.required", {
          field: t("profile.fields.gender"),
        }),
      })
      .default("prefer_not_say"),
    birthday: z.coerce.date({
      errorMap: (issue, ctx) => {
        if (issue.code === "invalid_type") {
          return {
            message: t("profile.validation.invalid_type", {
              field: t("profile.fields.birth_date"),
            }),
          };
        }
        if (issue.code === "invalid_date") {
          return {
            message: t("profile.validation.invalid_type", {
              field: t("profile.fields.birth_date"),
            }),
          };
        }
        return { message: ctx.defaultError };
      },
    }),
  });

export type MyUserProfileType = z.infer<ReturnType<typeof myUserProfileSchema>>;

const toMyUserProfile = (user: any): MyUserProfileType | null => {
  if (!user) return null;
  return {
    id: user.id,
    email: user.emailAddresses[0].emailAddress,
    emailVerified: user.emailAddresses[0].verification.status === "verified",
    name: user.fullName,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.unsafeMetadata.phoneNumber,
    website: user.unsafeMetadata.website,
    gender: user.unsafeMetadata.gender,
    birthday: new Date(user.unsafeMetadata.birthday),
  };
};

const isProfileComplete = ({
  user,
  t,
}: {
  user: MyUserProfileType | null;
  t: TFunction<"translation", undefined>;
}): boolean => {
  if (!user) return false;
  const schema = myUserProfileSchema(t).safeParse(user);
  return schema.success;
};

const useAppAuth = () => {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut } = useClerk();
  const { t } = useTranslation();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [_user, setUser] = useState<MyUserProfileType | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(isLoaded);

  useEffect(() => {
    if (isLoaded) {
      setIsAuthenticated(isSignedIn);
      setUser(toMyUserProfile(user));
      setIsLoadingUser(false);
    }
  }, [isLoaded, isSignedIn, user]);

  const handleSignOut = () => {
    signOut()
      .catch(async (err) => {
        createHandleErrorDialog({
          title: "Erro ao terminar sessão",
          error: err,
        });
      })
      .then(() => router.replace("/sign-in"));
  };

  const deleteUser = async () => {
    try {
      setIsLoadingUser(true);
      if (!_user?.id) {
        throw new Error("User not found");
      }

      await fetchApi(API_URLS.users, {
        method: "DELETE",
        body: JSON.stringify({ userId: _user.id }),
      });

      await handleSignOut();
    } catch (err) {
      createHandleErrorDialog({
        title: "Erro ao excluir conta",
        message:
          "Ocorreu um erro ao excluir a sua conta. Tente novamente mais tarde.",
        error: err as Error,
      });
      throw err;
    } finally {
      setIsLoadingUser(false);
    }
  };

  return {
    user: _user,
    isSignedIn: isAuthenticated,
    isLoadingUser,
    signOut: handleSignOut,
    deleteUser,
    SignedOut,
    SignedIn,
    userClient: user,
    isProfileComplete: isProfileComplete({ user: _user, t }),
  };
};

export default useAppAuth;
