import { Ionicons } from "@expo/vector-icons";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import dayjs from "dayjs";
import React, { useState } from "react";
import {
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native";
import globalStyles from "@/lib/globalStyles";
import { windowWidth } from "@/lib/device";

export default function MyDatePicker({
  date,
  label,
  onChange,
  style,
  theme = "primary",
  hasActiveColor = false,
  maximumDate,
  minimumDate,
  defaultValue,
  required = false,
}: {
  date?: Date;
  label: string;
  onChange: (timestamp: number) => void;
  style?: StyleProp<ViewStyle>;
  size?: "sm" | "md";
  theme?: "primary" | "secondary";
  hasActiveColor?: boolean;
  maximumDate?: Date;
  minimumDate?: Date;
  defaultValue?: Date;
  required?: boolean;
}) {
  const [show, setShow] = useState(false);

  const handleDateChange = (e: DateTimePickerEvent) => {
    if (e.type === "dismissed") return setShow(false);

    if (e.nativeEvent.timestamp) {
      onChange(e.nativeEvent.timestamp);
    }

    setShow(false);
  };

  const getThemeStyle = () => {
    if (hasActiveColor && date) {
      return styles.activeTheme;
    }

    switch (theme) {
      case "secondary":
        return styles.secondaryTheme;
      default:
        return styles.primaryTheme;
    }
  };

  const getTextStyle = () => {
    if (hasActiveColor && date) {
      return styles.activeText;
    }
    return theme === "primary" ? styles.primaryText : styles.secondaryText;
  };

  const getIconColor = () => {
    if (hasActiveColor && date) {
      return globalStyles.colors.white;
    }
    return theme === "primary"
      ? globalStyles.colors.primary1
      : globalStyles.colors.primary1;
  };

  return (
    <View style={[styles.container, style]}>
      {required && <View style={styles.required} />}
      {date && <Text style={styles.label}>{label}</Text>}
      <Pressable
        style={[styles.button, getThemeStyle(), style]}
        onPress={() => setShow(true)}
      >
        <Text style={[styles.text, getTextStyle()]}>
          {date
            ? dayjs(date).format("DD/MMMM/YYYY")
            : defaultValue
            ? dayjs(defaultValue).format("DD/MMMM/YYYY")
            : label}
        </Text>
        <Ionicons name="caret-down-sharp" size={15} color={getIconColor()} />
      </Pressable>
      {show && (
        <DateTimePicker
          value={date || defaultValue || new Date()}
          mode="date"
          is24Hour={true}
          locale="pt-BR"
          onChange={handleDateChange}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
          accentColor={globalStyles.colors.primary1}
          textColor={globalStyles.colors.primary1}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "relative",
  },
  required: {
    position: "absolute",
    top: 0,
    left: 0,
    width: windowWidth * 0.015,
    height: windowWidth * 0.015,
    borderRadius: globalStyles.rounded.xs,
    backgroundColor: globalStyles.colors.primary2,
    zIndex: 1,
  },
  button: {
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    width: "100%",
    height: globalStyles.size["6xl"],
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.tertiary2,
  },
  primaryTheme: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
  },
  secondaryTheme: {
    backgroundColor: globalStyles.colors.white,
  },
  activeTheme: {
    backgroundColor: "#c8b7bd",
    borderColor: globalStyles.colors.white,
  },
  text: {
    textAlign: "center",
    flex: 1,
    fontSize: globalStyles.size.lg,
    lineHeight: globalStyles.size.textAdjustLineHeight,
  },
  primaryText: {
    color: globalStyles.colors.primary1,
  },
  secondaryText: {
    color: globalStyles.rgba().primary1,
  },
  activeText: {
    color: globalStyles.colors.white,
  },
  smallSize: {
    paddingHorizontal: globalStyles.gap["2xs"],
    paddingVertical: 5,
  },
  mediumSize: {
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: 8,
  },
  smallTextSize: {
    fontSize: globalStyles.size.md,
  },
  mediumTextSize: {
    fontSize: globalStyles.size.lg,
  },
});
